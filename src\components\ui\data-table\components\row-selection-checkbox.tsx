import { memo } from "react";
import { Row, Table } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";

interface RowSelectionCheckboxProps<TData> {
  row: Row<TData>;
}

interface HeaderSelectionCheckboxProps<TData> {
  table: Table<TData>;
}

/**
 * Checkbox component for individual row selection
 */
export const RowSelectionCheckbox = memo(function RowSelectionCheckbox<TData>({
  row,
}: RowSelectionCheckboxProps<TData>) {
  return (
    <Checkbox
      checked={row.getIsSelected()}
      disabled={!row.getCanSelect()}
      onCheckedChange={row.getToggleSelectedHandler()}
      aria-label={`Select row ${row.id}`}
    />
  );
}) as any;

/**
 * Checkbox component for selecting all rows (header)
 */
export const HeaderSelectionCheckbox = memo(function HeaderSelectionCheckbox<TData>({
  table,
}: HeaderSelectionCheckboxProps<TData>) {
  const isAllSelected = table.getIsAllRowsSelected();
  const isSomeSelected = table.getIsSomeRowsSelected();

  return (
    <Checkbox
      checked={isAllSelected}
      ref={(el) => {
        if (el) el.indeterminate = isSomeSelected && !isAllSelected;
      }}
      onCheckedChange={table.getToggleAllRowsSelectedHandler()}
      aria-label="Select all rows"
    />
  );
}) as any;
