import { ColumnDef } from "@tanstack/react-table";
import { RowSelectionCheckbox, HeaderSelectionCheckbox } from "../components/row-selection-checkbox";

/**
 * Creates a selection column for the DataTable with checkboxes
 */
export function createSelectionColumn<TData>(): ColumnDef<TData, unknown> {
  return {
    id: "select",
    header: ({ table }) => <HeaderSelectionCheckbox table={table} />,
    cell: ({ row }) => <RowSelectionCheckbox row={row} />,
    enableSorting: false,
    enableHiding: false,
    meta: {
      enablePinning: true,
      width: 50,
    },
  };
}

/**
 * Adds a selection column to the beginning of the columns array
 */
export function addSelectionColumn<TData, TValue>(
  columns: ColumnDef<TData, TValue>[]
): ColumnDef<TData, TValue | unknown>[] {
  return [createSelectionColumn<TData>(), ...columns];
}
